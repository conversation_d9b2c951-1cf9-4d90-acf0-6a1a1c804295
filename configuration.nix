{ config, pkgs, ... }:

let
  unstable = import <nixos-unstable> {
    config = config.nixpkgs.config;
  };
in

{
  imports = [
    ./hardware-configuration.nix
    <home-manager/nixos>
  ];

  # Hardware Configuration
  hardware.sensor.iio.enable = true;

  # System Configuration
  system.stateVersion = "25.05";
  system.autoUpgrade = {
    enable = true;
    allowReboot = false;
    dates = "00:00:00";
    persistent = true;
  };

  nix.gc = {
    automatic = true;
    dates = "daily";
    options = "--delete-older-than 7d";
  };

  boot = {
    loader = {
      systemd-boot.enable = true;
      efi.canTouchEfiVariables = true;
    };
    kernelPackages = pkgs.linuxPackages_zen;
  };

  # Network Configuration
  networking = {
    hostName = "iDell";
    networkmanager.enable = true;
    # AdGuard DNS servers for ad-blocking and privacy
    nameservers = [ "************" "************" ];
    firewall = {
      allowedTCPPorts = [ 22000 8384 1714 1764 8080 39630 54104 11434 9000 8000 ];
      allowedUDPPorts = [ 22000 21027 1714 1764 1716 ];
    };
  };

  # Localization
  time.timeZone = "Africa/Algiers";
  i18n = {
    defaultLocale = "en_US.UTF-8";
    extraLocaleSettings = {
      LC_ADDRESS = "ar_DZ.UTF-8";
      LC_IDENTIFICATION = "ar_DZ.UTF-8";
      LC_MEASUREMENT = "ar_DZ.UTF-8";
      LC_MONETARY = "ar_DZ.UTF-8";
      LC_NAME = "ar_DZ.UTF-8";
      LC_NUMERIC = "ar_DZ.UTF-8";
      LC_PAPER = "ar_DZ.UTF-8";
      LC_TELEPHONE = "ar_DZ.UTF-8";
      LC_TIME = "ar_DZ.UTF-8";
    };
  };

  # Desktop Environment
  services.xserver = {
    enable = true;
    xkb = {
      layout = "fr";
      variant = "";
    };
  };
  services.displayManager.sddm.enable = true;
  services.desktopManager.plasma6.enable = true;

  # Enable KDE Applications
  programs.kdeconnect.enable = true;
  environment.plasma6.excludePackages = [];
  programs.partition-manager.enable = true;

  # System Services
  services = {
    printing.enable = false;
    libinput.enable = true;
    openssh.enable = true;
    fprintd.enable = true;

    pipewire = {
      enable = true;
      alsa = {
        enable = true;
        support32Bit = true;
      };
      pulse.enable = true;
    };

    power-profiles-daemon.enable = true;

    # Syncthing file synchronization service
    syncthing = {
      enable = true;
      user = "yusuf";
      dataDir = "/home/<USER>/.local/share/syncthing";
      configDir = "/home/<USER>/.config/syncthing";
      openDefaultPorts = true;
      settings = {
        # Devices configuration
        devices = {
          "iPoco" = {
            id = "VLCZOWO-JNGTHN6-QC5AGAZ-ZSRIBRN-5HJOPX6-OC4AE4Z-PMDRJW3-FUWP6Q3";
            addresses = [ "dynamic" ];
          };
          "iDell" = {
            id = "VPXAMC7-NSFBWYL-MJRN454-7X3F4GT-ZP5BRVH-BYPK75J-USIYEEI-56XJTAC";
            addresses = [ "dynamic" ];
          };
        };

        # Folders configuration
        folders = {
          "Videos" = {
            id = "namry-5ckpj";
            path = "/home/<USER>/iData/Videos";
            devices = [ "iPoco" "iDell" ];
            type = "sendreceive";
            rescanIntervalS = 3600;
            fsWatcherEnabled = true;
            ignorePerms = false;
          };
          "iDoc" = {
            id = "utet2-mdwaw";
            path = "/home/<USER>/iData/iDoc";
            devices = [ "iPoco" "iDell" ];
            type = "sendreceive";
            rescanIntervalS = 3600;
            fsWatcherEnabled = true;
            ignorePerms = false;
          };
        };

        # GUI configuration
        gui = {
          user = "yusuf";
          password = ""; # Empty password for local access only
          address = "127.0.0.1:8384";
          tls = false;
        };

        # Options configuration
        options = {
          urAccepted = -1; # Disable usage reporting
          globalAnnounceEnabled = true;
          localAnnounceEnabled = true;
          localAnnouncePort = 21027;
          relaysEnabled = true;
          startBrowser = true;
          natEnabled = true;
          crashReportingEnabled = true;
          autoUpgradeIntervalH = 12;
          keepTemporariesH = 24;
          limitBandwidthInLan = false;
          setLowPriority = true;
        };
      };
    };

  };

  # User Configuration
  users.users.yusuf = {
    isNormalUser = true;
    description = "yusuf";
    extraGroups = [ "networkmanager" "wheel" "fprintd" "adbusers" "docker" ];
    shell = pkgs.zsh;
  };

  # System Programs and Features
  environment.systemPackages = with pkgs; [
    fprintd
    ventoy
  ];

  programs = {
    zsh.enable = true;
    adb.enable = true;
    nix-ld.enable = true;
  };

  # Environment variables for Flutter development
  environment.variables = {
    ANDROID_HOME = "$HOME/Android/Sdk";
    ANDROID_SDK_ROOT = "$HOME/Android/Sdk";
    FLUTTER_ROOT = "${unstable.flutter}";
  };

  security.rtkit.enable = true;

  nixpkgs.config = {
    allowUnfree = true;
    android_sdk.accept_license = true;
    permittedInsecurePackages = [
      "ventoy-1.1.05"
    ];
  };

  fonts = {
    enableDefaultPackages = true;
    packages = with pkgs; [
      noto-fonts
      noto-fonts-cjk-sans
      noto-fonts-emoji
      noto-fonts-extra
      corefonts
      powerline-fonts
    ];
    fontconfig = {
      defaultFonts = {
        serif = [ "Noto Serif" ];
        sansSerif = [ "Noto Sans" ];
        monospace = [ "Noto Sans Mono" ];
        emoji = [ "Noto Color Emoji" ];
      };
    };
  };

  # Home Manager Configuration
  home-manager = {
    backupFileExtension = "backup";
    users.yusuf = { pkgs, ... }: {
      nixpkgs.config.allowUnfree = true;
      home.stateVersion = "25.05";
      home.packages = with pkgs; [
        unstable.vscode
        nixd
        nixpkgs-fmt
        kdePackages.applet-window-buttons6
        kdePackages.kdeconnect-kde
        syncthingtray
        backintime
        sshfs
        vlc
        libreoffice
        obsidian
        scrcpy
        usbimager
        qbittorrent-enhanced
        unstable.code-cursor
        unstable.windsurf
        unstable.zed-editor
        unstable.gemini-cli
        jdk17
        unstable.flutter
        unstable.android-studio
        unstable.android-tools
        (writeScriptBin "retry-until-success" ''
          #!${pkgs.zsh}/bin/zsh
          if [ $# -eq 0 ]; then
              echo "Usage: $0 'command to execute'"
              exit 1
          fi
          command_to_run="$@"
          attempt=1
          echo "Starting to execute: $command_to_run"
          while true; do
              # Execute the command
              if eval "$command_to_run"; then
                  echo "Command succeeded on attempt $attempt"
                  exit 0
              else
                  echo "Attempt $attempt failed. Retrying..."
                  attempt=$((attempt + 1))
                  sleep 1
              fi
          done
        '')
      ];

      programs = {
      browserpass.enable = true;
      brave = {
        enable = true;
        extensions = [
          { id = "cjpalhdlnbpafiamejdnhcphjbkeiagm"; } # uBlock Origin
          { id = "eimadpbcbfnmbkopoojfekhnkhdbieeh"; } # Dark Reader
          { id = "nngceckbapebfimnlniiiahkandclblb"; } # Bitwarden
          { id = "kchgllkpfcggmdaoopkhlkbcokngahlg"; } # DF Tube
          { id = "pehaalcefcjfccdpbckoablngfkfgfgj"; } # Block image
          { id = "mnjggcdmjocbbbhaepdhchncahnbgone"; } # SponsorBlock for YouTube
          { id = "ijlnjklmlhhfodgfpidpnccipnodohgl"; } # YouTube Silence Skipper
        ];
      };

        git = {
          enable = true;
          extraConfig = {
            credential.helper = "store";
            user = {
              name = "Yusuf";
              email = "<EMAIL>";
            };
          };
        };

        zsh = {
          enable = true;
          autosuggestion.enable = true;
          enableCompletion = true;
          syntaxHighlighting.enable = true;

          oh-my-zsh = {
            enable = true;
            theme = "robbyrussell";
            plugins = [
              "git"
              "sudo"
              "history"
              "dirhistory"
            ];
          };

          shellAliases = {
            switch = "sudo nixos-rebuild switch";
            upgrade = "sudo nixos-rebuild switch --upgrade";
            pod = "scrcpy --new-display=1920x1080/240 --start-app=com.itunestoppodcastplayer.app";
            ls = "ls --color=auto";
            ll = "ls -la";
            retry = "retry-until-success";
          };

          initContent = ''
            # Set history size and file
            HISTSIZE=10000
            SAVEHIST=10000
            HISTFILE=~/.zsh_history

            # Basic auto/tab completion
            autoload -U compinit
            zstyle ':completion:*' menu select
            zmodload zsh/complist
            compinit
            _comp_options+=(globdots)

            # Enable searching through history
            bindkey '^r' history-incremental-pattern-search-backward
          '';
        };
      };

      # Desktop entries for Ventoy
      xdg.desktopEntries = {
        ventoy = {
          name = "Ventoy";
          comment = "Create multiboot USB drives";
          exec = "konsole -e sudo ventoy";
          icon = "drive-removable-media";
          categories = [ "System" "Utility" ];
          terminal = false;
        };

        ventoy-web = {
          name = "Ventoy Web UI";
          comment = "Ventoy Web Interface for creating multiboot USB drives";
          exec = "sh -c 'ventoy-web && sleep 2 && xdg-open http://127.0.0.1:24680'";
          icon = "applications-internet";
          categories = [ "System" "Utility" "Network" ];
          terminal = false;
        };
      };
    };
  };
}
